from odoo import models, fields, api
from odoo.exceptions import UserError
import base64
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image, BaseDocTemplate, PageTemplate, Frame
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import mm, inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_LEFT, TA_RIGHT, TA_CENTER
import io
from datetime import datetime
import os
from reportlab.platypus import Paragraph
from reportlab.lib.styles import ParagraphStyle
import logging
_logger = logging.getLogger(__name__)
class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def action_generate_custom_pdf(self):
        """Generate custom PDF invoice using ReportLab"""
        try:
            pdf_buffer = self._generate_tsi_pdf()
            
            # Create attachment
            attachment = self.env['ir.attachment'].create({
                'name': f'Devis_{self.name}_{datetime.now().strftime("%Y%m%d")}.pdf',
                'type': 'binary',
                'datas': base64.b64encode(pdf_buffer.getvalue()),
                'res_model': 'sale.order',
                'res_id': self.id,
                'mimetype': 'application/pdf'
            })
            
            # Return action to download the PDF
            return {
                'type': 'ir.actions.act_url',
                'url': f'/web/content/{attachment.id}?download=true',
                'target': 'new',
            }
            
        except Exception as e:
            raise UserError(f'Error generating PDF: {str(e)}')

    def _generate_tsi_pdf(self):
        """Generate the PDF content with TSI format"""
        buffer = io.BytesIO()

        # Use BaseDocTemplate for custom headers and footers
        doc = BaseDocTemplate(buffer, pagesize=A4)

        # Define margins - leave space for header and footer
        header_height = 80  # Height reserved for header
        footer_height = 100  # Height reserved for footer
        margin = 20  # Side margins

        # Create frame for content (between header and footer)
        content_frame = Frame(
            margin, footer_height,  # x, y position
            A4[0] - 2*margin, A4[1] - header_height - footer_height,  # width, height
            leftPadding=0, rightPadding=0, topPadding=10, bottomPadding=10
        )

        # Create page template with header and footer functions
        page_template = PageTemplate(
            id='main_template',
            frames=[content_frame],
            onPage=self._draw_header_footer
        )

        doc.addPageTemplates([page_template])
        
        # Build content story
        story = self._build_content_story()

        # Build PDF with content only (header/footer will be drawn automatically)
        doc.build(story)
        buffer.seek(0)
        return buffer

    def _draw_header_footer(self, canvas, doc):
        """Draw header and footer on every page"""
        canvas.saveState()

        # Get page dimensions
        page_width = A4[0]
        page_height = A4[1]

        # Draw header
        self._draw_header(canvas, page_width, page_height)

        # Draw footer
        self._draw_footer(canvas, page_width, page_height)

        canvas.restoreState()

    def _draw_header(self, canvas, page_width, page_height):
        """Draw header content"""
        styles = getSampleStyleSheet()
        normal_style = ParagraphStyle(
            'NormalStyle',
            parent=styles['Normal'],
            fontSize=11,
            alignment=TA_LEFT
        )

        # Header positioning
        header_y = page_height - 60  # Position from top
        margin = 20

        # Company logo
        if self.company_id.logo:
            try:
                logo_data = base64.b64decode(self.company_id.logo)
                logo_buffer = io.BytesIO(logo_data)
                # Draw logo
                canvas.drawImage(logo_buffer, margin, header_y - 30, width=80, height=40)
            except:
                pass

        # Company info
        company_info_text = f'{self.company_id.name or "Tech Solaire Innovation"}\n{self.company_id.street or "El Ghazela Ariana 2083"}\nEmail: {self.company_id.email or "<EMAIL>"}\nTéléphone: {self.company_id.phone or "29 678 750 / 24 401 177"}\nMatricule Fiscal: {self.company_id.vat or "1871496/R/A/M/000"}'

        # Draw company info text
        text_x = margin + 100  # Position after logo
        text_lines = company_info_text.split('\n')
        for i, line in enumerate(text_lines):
            canvas.drawString(text_x, header_y - (i * 12), line)

        # Draw header line
        canvas.setStrokeColor(colors.grey)
        canvas.line(margin, header_y - 50, page_width - margin, header_y - 50)

    def _draw_footer(self, canvas, page_width, page_height):
        """Draw footer content"""
        styles = getSampleStyleSheet()
        normal_style = ParagraphStyle(
            'NormalStyle',
            parent=styles['Normal'],
            fontSize=10,
            alignment=TA_LEFT
        )

        # Footer positioning
        footer_y = 80  # Position from bottom
        margin = 20

        # Draw footer line
        canvas.setStrokeColor(colors.grey)
        canvas.line(margin, footer_y + 20, page_width - margin, footer_y + 20)

        # Company footer info
        company_name = self.company_id.name or "Tech Solaire Innovation"
        company_address = self.company_id.street or "El Ghazela Ariana 2083"
        company_vat = self.company_id.vat or "1871496/R/A/M/000"
        company_email = self.company_id.email or "<EMAIL>"
        company_website = self.company_id.website or "http://www.techsolaireinnovation.com"

        # Left footer
        left_footer_text = f'{company_name}\n{company_address}\nMatricule fiscale : {company_vat}\nEmail : {company_email}\nSite web : {company_website}'
        left_lines = left_footer_text.split('\n')
        for i, line in enumerate(left_lines):
            canvas.drawString(margin, footer_y - (i * 12), line)

        # Right footer
        right_footer_text = 'INFORMATIONS BANCAIRE\nBANQUE : Zitouna BANK\nRIB : 25 014 000 ********** 24'
        right_lines = right_footer_text.split('\n')
        right_x = page_width - 200  # Position from right
        for i, line in enumerate(right_lines):
            canvas.drawString(right_x, footer_y - (i * 12), line)

    def _build_content_story(self):
        """Build the main content story (without header/footer)"""
        story = []
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'TitleStyle',
            parent=styles['Normal'],
            fontSize=18,
            fontName='Helvetica-Bold',
            alignment=TA_CENTER
        )

        normal_style = ParagraphStyle(
            'NormalStyle',
            parent=styles['Normal'],
            fontSize=11,
            alignment=TA_LEFT
        )

        wrap_style = ParagraphStyle(
            name='wrap_style',
            fontName='Helvetica',
            fontSize=10,
            leading=12,
        )

        # Get page width for content
        page_width = A4[0]
        content_width = page_width - 40  # Content area with margins

        # Document Title (header and footer are now drawn automatically)
        if self.state == "done":
            title_text = f'BON DE COMMANDE {self.opportunity_id.power_level} Kwc<br/><br/>N° {self.name}'
        else:
            title_text = f'DEVIS {self.opportunity_id.power_level} Kwc<br/><br/>N° {self.name}'
        title_para = Paragraph(title_text, title_style)
        story.append(title_para)
        story.append(Spacer(1, 30))
        
        # Client Information
        partner_name = ""
        if self.opportunity_id.typ_paie == 'credit':
            partner_name = 'ZITOUNA P/C '
        _logger.info("Partner Name: %s", partner_name)
        partner_name += self.partner_id.name or ''
        client_info = f'''
        <b>Client :</b> {partner_name}<br/>
        <b>Code :</b> {self.partner_id.ref or 'N/A'}<br/>
        <b>Adresse :</b> {self._get_partner_address()}<br/>
        <b>Date Création :</b> {self.date_order.strftime('%d/%m/%Y') if self.date_order else ''}
        '''
        client_para = Paragraph(client_info, normal_style)
        story.append(client_para)
        story.append(Spacer(1, 20))
        
        # Items Table
        items_data = [['RÉFÉRENCE', 'NOM', 'QT', 'PU HT', 'TVA', 'M.TTC']]
        _logger.info("1")
        total_ht = 0
        total_tva = 0
        
        for line in self.order_line:
            if line.product_id:
                # Calculate values
                unit_price = line.price_unit
                quantity = line.product_uom_qty
                tax_rate = sum(line.tax_id.mapped('amount')) if line.tax_id else 0
                line_total = line.price_subtotal
                line_tax = line.price_total - line.price_subtotal
                line_total_ttc = line.price_total
                
                total_ht += line_total
                total_tva += line_tax
                
                items_data.append([
                    Paragraph(line.product_id.default_code or '', wrap_style),
                    Paragraph(line.name or '', wrap_style),  # Wrap description
                    f'{quantity:.0f}',
                    f'{unit_price:.3f}',
                    f'{tax_rate:.2f}%',
                    f'{line_total_ttc:.3f}'
                ])
        # Items Table - Full width
        left_right_padding = 5 * mm
        available_width = content_width - 2 * left_right_padding
        col_widths = [
            available_width * 0.18,  # RÉFÉRENCE - 15%
            available_width * 0.42,  # NOM - 45%
            available_width * 0.08,  # QT - 8%
            available_width * 0.12,  # PU HT - 12%
            available_width * 0.08,  # TVA - 8%
            available_width * 0.12   # M.TTC - 12%
        ]

        items_table = Table(items_data, colWidths=col_widths)
        items_table.setStyle(TableStyle([
            ('BACKGROUND', (0,0), (-1,0), colors.lightgrey),
            ('TEXTCOLOR', (0,0), (-1,0), colors.black),
            ('ALIGN', (0,0), (-1,-1), 'LEFT'),
            ('ALIGN', (2,0), (-1,-1), 'RIGHT'),  # Right align numbers
            ('FONTNAME', (0,0), (-1,0), 'Helvetica-Bold'),
            ('FONTSIZE', (0,0), (-1,-1), 10),
            ('GRID', (0,0), (-1,-1), 1, colors.white),
            ('LINEABOVE', (0,0), (-1,0), 3, colors.Color(0.2, 0.745, 0.941)),  # Blue top border
            ('ROWBACKGROUNDS', (0,1), (-1,-1), [colors.white, colors.Color(0.96, 0.96, 0.96)])
        ]))
        story.append(items_table)
        story.append(Spacer(1, 20))
        
        # Totals Section - with more space between tables
        totals_data = [
            [
                # TVA breakdown (left side)
                self._create_tva_table(),
                # Empty space between tables
                '',
                # Total amounts (right side)
                self._create_totals_table(total_ht, total_tva)
            ]
        ]
        col_widths = [
            available_width * 0.3,  # TVA table
            available_width * 0.3,  # Empty space
            available_width * 0.4,  # Totals table
        ]
        totals_table = Table(totals_data, colWidths=col_widths)
        totals_table.setStyle(TableStyle([
            ('VALIGN', (0,0), (-1,-1), 'TOP'),
        ]))
        story.append(totals_table)
        story.append(Spacer(1, 30))

        # Content Information (footer info moved to actual footer)
        footer_text = f"Production Annuelle : {self.opportunity_id.prod_annuel} KWh<br/>"
        footer_text += f"Somme : {self.amount_words}<br/>"
        footer_text += '''
        <b>Validité de l'offre:</b><br/>
        <b>Garantie</b><br/>
        Les panneaux photovoltaïques sont certifiés par la STEG et l'ANME et sont garantis pendant 30 ans.<br/>
        Pour plus d'informations vous pouvez contacter notre équipe commerciale.
        '''
        story.append(Paragraph(footer_text, normal_style))
        story.append(Spacer(1, 20))

        # Signature section
        signature_text = '<para align="right"><b>Cachet et signature</b></para>'
        story.append(Paragraph(signature_text, normal_style))
        story.append(Spacer(1, 30))

        return story

    def _get_partner_address(self):
        """Get formatted partner address"""
        partner = self.partner_id
        address_parts = []
        if partner.street:
            address_parts.append(partner.street)
        if partner.street2:
            address_parts.append(partner.street2)
        if partner.city:
            address_parts.append(partner.city)
        if partner.zip:
            address_parts.append(partner.zip)
        if partner.country_id:
            address_parts.append(partner.country_id.name)
        return ', '.join(address_parts) if address_parts else 'N/A'

    def _create_tva_table(self):
        """Create TVA breakdown table"""
        # Group taxes by rate
        tax_groups = {}
        for line in self.order_line:
            for tax in line.tax_id:
                rate = tax.amount
                if rate not in tax_groups:
                    tax_groups[rate] = {'base': 0, 'amount': 0}
                tax_groups[rate]['base'] += line.price_subtotal
                tax_groups[rate]['amount'] += line.price_total - line.price_subtotal
        
        tva_data = [['TVA %', 'BASE', 'MONTANT']]
        for rate, values in tax_groups.items():
            tva_data.append([
                f'{rate:.0f} %',
                f'{values["base"]:.3f} TND',
                f'{values["amount"]:.3f} TND'
            ])
        
        tva_table = Table(tva_data, colWidths=[0.8*inch, 1.2*inch, 1.2*inch])
        tva_table.setStyle(TableStyle([
    ('BACKGROUND', (0,0), (-1,0), colors.lightgrey),
    ('FONTNAME', (0,0), (-1,0), 'Helvetica-Bold'),
    ('FONTSIZE', (0,0), (-1,-1), 10),
    ('ALIGN', (0,0), (0,-1), 'LEFT'),       # First column left-aligned
    ('ALIGN', (1,1), (-1,-1), 'RIGHT'),     # Numeric columns right-aligned
    ('LEFTPADDING', (0,0), (-1,-1), 0),     # remove default left padding
    ('RIGHTPADDING', (0,0), (-1,-1), 0),    # remove default right padding
    ('TOPPADDING', (0,0), (-1,-1), 2),
    ('BOTTOMPADDING', (0,0), (-1,-1), 2),
    ('GRID', (0,0), (-1,-1), 1, colors.white),
    ('LINEABOVE', (0,0), (-1,0), 3, colors.Color(0.2, 0.745, 0.941)),
]))
        
        return tva_table

    def _create_totals_table(self, total_ht, total_tva):
        """Create totals table"""
        total_ttc = total_ht + total_tva
        remise = 0  # Calculate if you have discounts
        
        totals_data = [
            ['TOTAL BRUTE', f'{total_ttc:.3f} TND'],
            ['REMISE', f'{remise:.3f} TND'],
            ['TOTAL HT', f'{total_ht:.3f} TND'],
            ['TOTAL TVA', f'{total_tva:.3f} TND'],
            ['TIMBRE FISCAL', '0 TND'],
            ['TOTAL TTC', f'{total_ttc:.3f} TND']
        ]
        
        totals_table = Table(totals_data, colWidths=[1.5*inch, 1.5*inch])
        totals_table.setStyle(TableStyle([
            ('ALIGN', (0,0), (-1,-1), 'RIGHT'),
            ('FONTSIZE', (0,0), (-1,-1), 11),
            ('LINEABOVE', (0,0), (-1,0), 3, colors.Color(0.2, 0.745, 0.941)),
            ('LINEABOVE', (0,-1), (-1,-1), 2, colors.black),
            ('FONTNAME', (0,-1), (-1,-1), 'Helvetica-Bold'),
            ('ROWBACKGROUNDS', (0,0), (-1,-2), [colors.white, colors.Color(0.94, 0.94, 0.94)]),
        ]))
        
        return totals_table